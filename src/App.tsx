import {GenerateColumn, Table} from "./table";
import {FormModel, model} from "./table/model.ts";
import {observer} from "mobx-react-lite";
import {useEffect, useState} from "react";
import { TestScanner } from "./scanner/test-scanner";

function App() {
  const [currentView, setCurrentView] = useState<'table' | 'scanner'>('table');
  const [form] = useState(() => new FormModel())

  useEffect(() => {
    form.setRowsCount(5)
    form.setColsCount(5)
    form.generate()
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex space-x-8">
              <button
                onClick={() => setCurrentView('table')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  currentView === 'table'
                    ? 'border-blue-500 text-gray-900'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Таблица расчетов
              </button>
              <button
                onClick={() => setCurrentView('scanner')}
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                  currentView === 'scanner'
                    ? 'border-blue-500 text-gray-900'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Сканер чеков
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="py-6">
        {currentView === 'table' ? (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {model.rows.length > 0 ? <Table /> : <GenerateColumn />}
          </div>
        ) : (
          <TestScanner />
        )}
      </main>
    </div>
  )
}

export default observer(App)
