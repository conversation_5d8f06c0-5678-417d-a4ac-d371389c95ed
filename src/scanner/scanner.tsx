// @ts-nocheck

import { useRef, useState } from 'react';
import Tesseract from 'tesseract.js';

type Item = {
    name: string;
    quantity: number;
    price: number;
};

export function AugmentReceiptScanner() {
    const [text, setText] = useState('');
    const [items, setItems] = useState<Item[]>([]);
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const imageRef = useRef<HTMLImageElement>(null);

    const preprocessImage = (image: HTMLImageElement): HTMLCanvasElement => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        // Увеличиваем масштаб для лучшего OCR
        const scale = 3.0;
        canvas.width = image.naturalWidth * scale;
        canvas.height = image.naturalHeight * scale;

        // Рисуем изображение с высоким качеством
        ctx.imageSmoothingEnabled = false;
        ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Улучшенная бинаризация с адаптивным порогом
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Преобразование в оттенки серого
            const gray = 0.299 * r + 0.587 * g + 0.114 * b;

            // Увеличение контраста
            const contrast = 2.5;
            const enhancedGray = ((gray - 128) * contrast) + 128;

            // Адаптивная бинаризация
            const threshold = enhancedGray > 160 ? 255 : 0;

            data[i] = threshold;
            data[i + 1] = threshold;
            data[i + 2] = threshold;
        }

        ctx.putImageData(imageData, 0, 0);
        return canvas;
    };

    const cleanText = (text: string): string => {
        return text
            // Исправление цифр
            .replace(/[О0о]/g, '0')
            .replace(/[Б]/g, '6')
            .replace(/[З]/g, '3')
            .replace(/[С]/g, '5')
            .replace(/[Т]/g, '7')
            .replace(/[В]/g, '8')
            .replace(/[Э]/g, '9')
            .replace(/[I|l]/g, '1')
            .replace(/[S$]/g, '5')
            .replace(/[@°]/g, '0')

            // Очистка лишних символов и артефактов OCR
            .replace(/[|_~`¢]/g, '')
            .replace(/[\/\\]/g, '')
            .replace(/[«»"']/g, '')
            .replace(/\s+/g, ' ')
            .trim();
    };

    function parseText(text: string) {
        const cleanedText = cleanText(text);
        const s1 = cleanedText.split('\n').map(l => l.trim()).filter(Boolean);
        const products: Item[] = [];

        s1.forEach(s => {
            const s2 = s.split(' ').map(l => l.trim()).filter(Boolean);
            const product: Item = {
                name: '',
                price: 0,
                quantity: 0
            };

            console.log(s2);

            s2.forEach((s, i) => {
                if (i === s2.length - 1) {
                    product.price = parseFloat(s.replace(',', '.'));
                }

                if (i === s2.length - 2) {
                    product.quantity = parseFloat(s.replace(',', '.'));
                }

                if (i !== s2.length - 2 && i !== s2.length - 1) {
                    product.name += s + ' ';
                }
            });

            // Добавляем только если есть валидные данные
            if (product.name.trim() && !isNaN(product.price) && !isNaN(product.quantity) && product.price > 0 && product.quantity > 0) {
                product.name = product.name.trim();
                products.push(product);
            }
        });

        setItems(products);
    }

    const handleScan = async () => {
        if (!imageRef.current) {
            setError('Пожалуйста, загрузите изображение чека.');
            return;
        }
        setLoading(true);
        setError(null);

        try {
            const canvas = preprocessImage(imageRef.current);
            const dataUrl = canvas.toDataURL('image/png');

            const { data } = await Tesseract.recognize(dataUrl, 'rus+eng', {
                logger: m => console.log(m),
                tessedit_char_whitelist: '0123456789,.abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZабвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ ',
                tessedit_pagesegmode: '4', // PSM 4: Предполагает один столбец текста
                tessedit_ocr_engine_mode: '1', // LSTM для лучшей точности
            });

            const rawText = data.text;
            parseText(rawText);
            setText(rawText);
        } catch (err) {
            console.error('Ошибка распознавания:', err);
            setError('Ошибка обработки чека. Проверьте четкость и освещение изображения.');
        } finally {
            setLoading(false);
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (!file.type.startsWith('image/')) {
                setError('Пожалуйста, загрузите файл изображения.');
                return;
            }
            setImageUrl(URL.createObjectURL(file));
            setText('');
            setItems([]);
            setError(null);
        }
    };

    return (
        <div className="p-6 max-w-2xl mx-auto space-y-6 bg-white rounded-lg shadow-md">
            <h2 className="text-2xl font-bold text-gray-800">Сканер чеков</h2>

            <div className="flex flex-col items-center space-y-4">
                <label className="block">
                    <span className="text-gray-700">Выберите изображение чека</span>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                </label>

                {imageUrl && (
                    <img
                        ref={imageRef}
                        src={imageUrl}
                        alt="Чек"
                        className="max-w-full max-h-96 rounded-md border border-gray-300"
                    />
                )}

                <button
                    onClick={handleScan}
                    disabled={!imageUrl || loading}
                    className={`w-full py-2 px-4 rounded-md text-white font-semibold ${
                        !imageUrl || loading
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                    } transition-colors`}
                >
                    {loading ? 'Обработка...' : 'Сканировать чек'}
                </button>
            </div>

            {error && (
                <div className="bg-red-100 p-4 rounded-md text-red-700">
                    <h3 className="font-semibold">Ошибка:</h3>
                    <p>{error}</p>
                </div>
            )}

            {text && (
                <div className="bg-gray-100 p-4 rounded-md">
                    <h3 className="font-semibold text-gray-800 mb-2">Распознанный текст:</h3>
                    <pre className="text-sm text-gray-600 whitespace-pre-wrap">{text}</pre>
                </div>
            )}

            {items.length > 0 && (
                <div className="bg-green-100 p-4 rounded-md">
                    <h3 className="font-semibold text-gray-800 mb-2">🧾 Найдено товаров:</h3>
                    <ul className="list-disc list-inside text-gray-700">
                        {items.map((item, i) => (
                            <li key={i}>
                                {item.name} — {item.quantity} шт × {item.price.toLocaleString('ru-RU', {
                                style: 'currency',
                                currency: 'UZS',
                            })}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
}