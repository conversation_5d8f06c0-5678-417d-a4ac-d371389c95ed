// @ts-nocheck

import { useRef, useState } from 'react';
import Tesseract from 'tesseract.js';

type Item = {
    name: string;
    quantity: number;
    price: number;
};

export function GrokReceiptScanner() {
    const [text, setText] = useState('');
    const [items, setItems] = useState<Item[]>([]);
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const imageRef = useRef<HTMLImageElement>(null);

    const preprocessImage = (image: HTMLImageElement): HTMLCanvasElement => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        // Увеличиваем масштаб для лучшего OCR
        const scale = 3.0;
        canvas.width = image.naturalWidth * scale;
        canvas.height = image.naturalHeight * scale;

        // Рисуем изображение с высоким качеством
        ctx.imageSmoothingEnabled = false;
        ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Улучшенная бинаризация с адаптивным порогом
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Преобразование в оттенки серого
            const gray = 0.299 * r + 0.587 * g + 0.114 * b;

            // Увеличение контраста
            const contrast = 2.5;
            const enhancedGray = ((gray - 128) * contrast) + 128;

            // Адаптивная бинаризация
            const threshold = enhancedGray > 160 ? 255 : 0;

            data[i] = threshold;
            data[i + 1] = threshold;
            data[i + 2] = threshold;
        }

        ctx.putImageData(imageData, 0, 0);
        return canvas;
    };

    const cleanText = (text: string): string => {
        return text
            // Исправление цифр
            .replace(/[О0о]/g, '0')
            .replace(/[Б6]/g, '6')
            .replace(/[З3]/g, '3')
            .replace(/[С5]/g, '5')
            .replace(/[Т7]/g, '7')
            .replace(/[В8]/g, '8')
            .replace(/[Э9]/g, '9')
            .replace(/[I|l1]/g, '1')
            .replace(/[S$]/g, '5')
            .replace(/[@]/g, '0')
            .replace(/[°]/g, '0')

            // Очистка лишних символов
            .replace(/[|_~`]/g, '')
            .replace(/\s+/g, ' ')
            .trim();
    };

    function parseText(text: string) {
        const cleanedText = cleanText(text);
        const lines = cleanedText.split('\n').map(l => l.trim()).filter(Boolean);
        const products: Item[] = [];

        for (const line of lines) {
            // Пропускаем служебные строки
            if (line.match(/^(итого|всего|сумма|скидка|налог|к оплате|сдача|получено|дата|время|кассир|чек|№|магазин|адрес)/i)) {
                continue;
            }

            // Разбиваем строку на части
            const parts = line.split(/\s+/).filter(Boolean);

            if (parts.length >= 2) {
                let name = '';
                let quantity = 1;
                let price = 0;

                // Ищем цену (последний элемент, содержащий цифры)
                for (let i = parts.length - 1; i >= 0; i--) {
                    const part = parts[i].replace(/[^\d.,]/g, '').replace(',', '.');
                    const numValue = parseFloat(part);

                    if (!isNaN(numValue) && numValue > 0) {
                        if (price === 0) {
                            // Первое найденное число - это цена
                            price = numValue;
                            parts.splice(i, 1);
                        } else if (quantity === 1 && numValue < 100) {
                            // Второе число (если меньше 100) - это количество
                            quantity = numValue;
                            parts.splice(i, 1);
                            break;
                        }
                    }
                }

                // Оставшиеся части - это название товара
                name = parts.join(' ').trim();

                // Валидация и добавление товара
                if (name && price > 0 && quantity > 0) {
                    // Дополнительная очистка названия
                    name = name
                        .replace(/^[^\w\u0400-\u04FF]+/, '') // Убираем символы в начале
                        .replace(/[^\w\u0400-\u04FF\s]+$/, '') // Убираем символы в конце
                        .trim();

                    if (name.length > 1) {
                        products.push({
                            name: name,
                            quantity: quantity,
                            price: price
                        });
                    }
                }
            }
        }

        setItems(products);
    }